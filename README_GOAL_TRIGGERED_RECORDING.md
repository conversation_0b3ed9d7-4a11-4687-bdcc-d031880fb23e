# IMPACTOR 目标触发轨迹记录系统

## 概述
`test_goal_triggered_recording.sh` 是IMPACTOR系统的目标触发轨迹记录测试脚本。该脚本实现了通过rviz界面设置目标来触发轨迹记录的功能，当无人机和载荷到达指定位置时自动停止记录并生成轨迹文件。

## 主要特性

### ✅ 核心功能
- **rviz目标触发**: 通过在rviz中发布 `/move_base_simple/goal` 话题开始记录
- **自动轨迹记录**: 高频率记录完整的轨迹数据
- **智能目标检测**: 检测无人机和载荷是否到达目标位置
- **详细统计分析**: 自动计算平均速度、加速度和运动距离
- **工作目录输出**: 所有文件保存在工作目录 `/home/<USER>/impactor_ws/` 下

### 📁 生成文件
- **CSV轨迹文件**: `trajectory_log_YYYYMMDD_HHMMSS.csv`
- **PNG可视化图**: `trajectory_plot_YYYYMMDD_HHMMSS.png` (如果配置了绘图功能)

### 📊 统计分析功能 (新增)
脚本会自动分析轨迹数据并提供以下统计信息：
- **速度统计**: 平均速度、最大速度
- **加速度统计**: 平均加速度、最大加速度
- **距离统计**: 总行程距离、直线距离、路径效率
- **时间统计**: 飞行时长、数据采样率
- **位置信息**: 起始和结束位置坐标

## 快速开始

### 启动系统
```bash
cd /home/<USER>/impactor_ws
chmod +x test_goal_triggered_recording.sh
./test_goal_triggered_recording.sh
```

### 数据统计位于Visualizer::visualizeDoubleball()函数中
### 在payload_plan_pcd.launch中修改起点
<arg name="init_x" value="-6.0"/>  <!-- 修改初始位置与路径点一致 -->
<arg name="init_y" value="0.0"/>
<arg name="init_z" value="2.0"/>   <!-- 提高初始高度 -->

### 在payload_manager_pcd.yaml中修改起终点
waypoints_num: 2  
waypoint_0:  # 起点
   quad:
   x: -6.0
   y: 0.0
   z: 2.0
   load:
   x: -6.0
   y: 0.0
   z: 1.356
waypoint_1:  # 终点
   quad:
   x: 6.0
   y: -1.0
   z: 2.0
   load:
   x: 6.0
   y: -1.0
   z: 1.356

### 操作步骤
1. **等待系统初始化** (约10秒)
   - 脚本会自动启动roscore和所有必要节点
   - rviz会自动打开
   - 等待看到 "System initialized. Waiting for goal to be set in rviz..."

2. **在rviz中设置目标**
   - 使用rviz工具栏中的 "2D Nav Goal" 工具
   - 在地图上点击设置目标位置和方向
   - 系统会显示 "Goal received! System should now start planning and recording..."

3. **自动执行和记录**
   - 系统自动进行轨迹规划
   - 开始记录轨迹数据
   - 无人机和载荷开始向目标移动

4. **等待任务完成**
   - 脚本会监控系统进程
   - 当系统完成任务后会显示 "System has shut down automatically."
   - 脚本自动清理所有进程并退出

## 文件详情

### CSV轨迹文件格式
包含以下数据列：
```
timestamp          - 时间戳 (秒)
load_pos_x/y/z     - 载荷位置 (米)
load_vel_x/y/z     - 载荷速度 (米/秒)
load_acc_x/y/z     - 载荷加速度 (米/秒²)
quad_pos_x/y/z     - 四旋翼位置 (米)
quad_vel_x/y/z     - 四旋翼速度 (米/秒)
quad_acc_x/y/z     - 四旋翼加速度 (米/秒²)
quad_quat_x/y/z/w  - 四旋翼姿态四元数
```

### PNG可视化图内容
包含4个子图的综合分析：
1. **2D轨迹图**: 俯视图显示四旋翼和载荷的运动轨迹
2. **高度变化**: 时间vs高度的变化曲线
3. **速度分析**: 时间vs速度大小的变化曲线
4. **位置误差**: 时间vs到目标距离的对数图

### 统计分析输出示例
```
============================================================
TRAJECTORY STATISTICS ANALYSIS
============================================================
File: trajectory_log_20250801_191437.csv
Duration: 15.97 seconds
Data Points: 884
Sampling Rate: 55.3 Hz

------------------------------ QUADROTOR ------------------------------
Start Position: (-5.997, -0.004, 1.999) m
End Position:   (6.000, -1.000, 2.000) m

Average Velocity:     0.884 m/s
Maximum Velocity:     1.776 m/s
Average Acceleration: 1.125 m/s²
Maximum Acceleration: 3.861 m/s²

Total Travel Distance:    14.133 m
Straight-line Distance:   12.038 m
Path Efficiency:          85.2%

-------------------------------- LOAD --------------------------------
Start Position: (-6.000, -0.001, 1.360) m
End Position:   (6.000, -1.000, 1.356) m

Average Velocity:     1.078 m/s
Maximum Velocity:     3.401 m/s
Average Acceleration: 2.891 m/s²
Maximum Acceleration: 13.252 m/s²

Total Travel Distance:    17.228 m
Straight-line Distance:   12.041 m
Path Efficiency:          69.9%

---------------------------- SUMMARY ----------------------------
System completed 12.0m mission in 16.0s
Average system velocity: 0.98 m/s
Quadrotor-Load distance: 0.644 m
============================================================
```

## 脚本工作流程

### 系统启动阶段
1. **环境准备**: 设置ROS环境变量和包路径
2. **进程清理**: 清理可能存在的旧进程
3. **文件清理**: 删除旧的轨迹文件
4. **启动roscore**: 启动ROS核心服务
5. **启动系统**: 使用 `roslaunch impact_plan impactor_pcd.launch` 启动IMPACTOR系统

### 目标等待阶段
1. **系统初始化**: 等待10秒让系统完全启动
2. **目标监控**: 使用 `rostopic echo /move_base_simple/goal` 监听目标话题
3. **超时设置**: 最多等待300秒(5分钟)接收目标

### 任务执行阶段
1. **目标接收**: 检测到rviz发布的目标后开始任务
2. **进程监控**: 持续监控launch进程状态
3. **自动等待**: 等待系统自动完成任务并关闭

### 清理和报告阶段
1. **进程清理**: 清理所有相关进程
2. **文件检查**: 检查生成的轨迹文件
3. **结果报告**: 显示任务完成状态和文件信息

## 系统配置

### 文件保存位置
- **工作目录**: `/home/<USER>/impactor_ws/`
- **自动命名**: 使用时间戳避免文件覆盖
- **文件格式**: CSV格式的轨迹数据

## 故障排除

### 常见问题及解决方案

#### 1. rviz无法启动
```bash
export DISPLAY=:0
./test_goal_triggered_recording.sh
```

#### 2. Python绘图依赖缺失
```bash
pip3 install matplotlib pandas numpy
```

#### 3. 权限问题
```bash
chmod +x test_goal_triggered_recording.sh
chmod +x generate_trajectory_plot.py
```

#### 4. 端口占用问题
```bash
# 清理可能的残留进程
pkill -f "roslaunch\|roscore\|rviz"
# 等待几秒后重新运行
./test_goal_triggered_recording.sh
```

### 检查生成的文件
```bash
# 查看最新生成的文件
ls -la /home/<USER>/impactor_ws/trajectory_*

# 查看CSV文件内容
head -10 /home/<USER>/impactor_ws/trajectory_log_*.csv
tail -10 /home/<USER>/impactor_ws/trajectory_log_*.csv
```

## 技术实现

### 核心组件
- **visualizer.cpp**: 轨迹记录和目标检测核心逻辑
- **generate_trajectory_plot.py**: Python可视化脚本
- **test_goal_triggered_recording.sh**: 系统启动和管理脚本

### 关键技术特点
- **实时性能**: 高频率数据采集和处理
- **鲁棒检测**: 多条件目标到达判断
- **自动化管理**: 完全无人值守运行
- **信号机制**: 使用文件信号实现进程间通信

## 使用建议

### 最佳实践
1. **确保环境清洁**: 运行前确保没有其他ROS进程
2. **合理设置目标**: 选择地图范围内的可达位置
3. **耐心等待初始化**: 给系统充分的启动时间
4. **保存重要数据**: 及时备份生成的轨迹文件

### 性能优化
- 系统支持高频率数据记录 (50Hz+)
- 自动内存管理，避免数据积累
- 实时统计计算，提供即时反馈

## 脚本特点

### 优势
✅ **简单易用**: 一键启动，通过rviz图形界面操作
✅ **自动化程度高**: 自动启动、监控和清理
✅ **错误处理**: 包含超时处理和错误检查
✅ **文件管理**: 工作目录统一管理输出文件
✅ **详细反馈**: 提供详细的执行状态信息

### 适用场景
- IMPACTOR系统功能测试
- 轨迹记录和数据收集
- 算法验证和性能评估
- 演示和教学用途

## 注意事项

1. **确保环境清洁**: 运行前脚本会自动清理旧进程
2. **耐心等待**: 给系统充分的初始化时间
3. **合理设置目标**: 在rviz中选择地图范围内的可达位置
4. **监控输出**: 注意脚本的状态输出信息

这个脚本为IMPACTOR系统提供了便捷的目标触发轨迹记录功能，是系统测试和数据收集的重要工具。
