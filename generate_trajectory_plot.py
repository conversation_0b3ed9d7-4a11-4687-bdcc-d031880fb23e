#!/usr/bin/env python3
"""
Trajectory Plot Generator for IMPACTOR System
Generates visualization plots from trajectory CSV files
"""

import sys
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from pathlib import Path
import argparse

def generate_trajectory_plot(csv_file, output_dir="/home/<USER>/impactor_ws"):
    """
    Generate trajectory visualization plots from CSV file
    
    Args:
        csv_file: Path to the trajectory CSV file
        output_dir: Directory to save the plot files
    """
    try:
        # Read CSV file
        df = pd.read_csv(csv_file)
        
        # Extract filename without extension for plot naming
        csv_path = Path(csv_file)
        base_name = csv_path.stem.replace('trajectory_log_', 'trajectory_plot_')
        
        # Create figure with subplots
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle(f'IMPACTOR Trajectory Analysis - {csv_path.name}', fontsize=16)
        
        # Plot 1: 3D Trajectory
        ax1 = axes[0, 0]
        ax1.plot(df['quad_pos_x'], df['quad_pos_y'], 'b-', label='Quadrotor', linewidth=2)
        ax1.plot(df['load_pos_x'], df['load_pos_y'], 'r-', label='Load', linewidth=2)
        ax1.scatter(df['quad_pos_x'].iloc[0], df['quad_pos_y'].iloc[0], c='blue', s=100, marker='o', label='Start (Quad)')
        ax1.scatter(df['load_pos_x'].iloc[0], df['load_pos_y'].iloc[0], c='red', s=100, marker='o', label='Start (Load)')
        ax1.scatter(df['quad_pos_x'].iloc[-1], df['quad_pos_y'].iloc[-1], c='blue', s=100, marker='s', label='End (Quad)')
        ax1.scatter(df['load_pos_x'].iloc[-1], df['load_pos_y'].iloc[-1], c='red', s=100, marker='s', label='End (Load)')
        ax1.set_xlabel('X Position (m)')
        ax1.set_ylabel('Y Position (m)')
        ax1.set_title('2D Trajectory (Top View)')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        ax1.axis('equal')
        
        # Plot 2: Height vs Time
        ax2 = axes[0, 1]
        ax2.plot(df['timestamp'], df['quad_pos_z'], 'b-', label='Quadrotor Height', linewidth=2)
        ax2.plot(df['timestamp'], df['load_pos_z'], 'r-', label='Load Height', linewidth=2)
        ax2.set_xlabel('Time (s)')
        ax2.set_ylabel('Height (m)')
        ax2.set_title('Height vs Time')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # Plot 3: Velocity Magnitude
        ax3 = axes[1, 0]
        quad_vel_mag = np.sqrt(df['quad_vel_x']**2 + df['quad_vel_y']**2 + df['quad_vel_z']**2)
        load_vel_mag = np.sqrt(df['load_vel_x']**2 + df['load_vel_y']**2 + df['load_vel_z']**2)
        ax3.plot(df['timestamp'], quad_vel_mag, 'b-', label='Quadrotor Speed', linewidth=2)
        ax3.plot(df['timestamp'], load_vel_mag, 'r-', label='Load Speed', linewidth=2)
        ax3.set_xlabel('Time (s)')
        ax3.set_ylabel('Speed (m/s)')
        ax3.set_title('Speed vs Time')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # Plot 4: Position Error (Distance to Goal)
        ax4 = axes[1, 1]
        # Assume goal is the final position
        goal_quad = [df['quad_pos_x'].iloc[-1], df['quad_pos_y'].iloc[-1], df['quad_pos_z'].iloc[-1]]
        goal_load = [df['load_pos_x'].iloc[-1], df['load_pos_y'].iloc[-1], df['load_pos_z'].iloc[-1]]
        
        quad_error = np.sqrt((df['quad_pos_x'] - goal_quad[0])**2 + 
                            (df['quad_pos_y'] - goal_quad[1])**2 + 
                            (df['quad_pos_z'] - goal_quad[2])**2)
        load_error = np.sqrt((df['load_pos_x'] - goal_load[0])**2 + 
                            (df['load_pos_y'] - goal_load[1])**2 + 
                            (df['load_pos_z'] - goal_load[2])**2)
        
        ax4.semilogy(df['timestamp'], quad_error, 'b-', label='Quadrotor Error', linewidth=2)
        ax4.semilogy(df['timestamp'], load_error, 'r-', label='Load Error', linewidth=2)
        ax4.set_xlabel('Time (s)')
        ax4.set_ylabel('Distance to Goal (m)')
        ax4.set_title('Position Error vs Time (Log Scale)')
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        
        # Adjust layout
        plt.tight_layout()
        
        # Save plot
        plot_filename = f"{output_dir}/{base_name}.png"
        plt.savefig(plot_filename, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"✓ Trajectory plot saved: {plot_filename}")
        
        # Generate summary statistics
        duration = df['timestamp'].iloc[-1] - df['timestamp'].iloc[0]
        max_quad_speed = quad_vel_mag.max()
        max_load_speed = load_vel_mag.max()
        final_quad_error = quad_error.iloc[-1]
        final_load_error = load_error.iloc[-1]
        
        print(f"✓ Trajectory Statistics:")
        print(f"  - Duration: {duration:.2f} seconds")
        print(f"  - Data points: {len(df)}")
        print(f"  - Max quadrotor speed: {max_quad_speed:.2f} m/s")
        print(f"  - Max load speed: {max_load_speed:.2f} m/s")
        print(f"  - Final quadrotor error: {final_quad_error:.4f} m")
        print(f"  - Final load error: {final_load_error:.4f} m")
        
        return True
        
    except Exception as e:
        print(f"✗ Error generating trajectory plot: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description='Generate trajectory plots from CSV files')
    parser.add_argument('csv_file', help='Path to trajectory CSV file')
    parser.add_argument('--output-dir', default='/home/<USER>/impactor_ws', 
                       help='Output directory for plots (default: /home/<USER>/impactor_ws)')
    
    args = parser.parse_args()
    
    if not Path(args.csv_file).exists():
        print(f"✗ Error: CSV file not found: {args.csv_file}")
        sys.exit(1)
    
    success = generate_trajectory_plot(args.csv_file, args.output_dir)
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
