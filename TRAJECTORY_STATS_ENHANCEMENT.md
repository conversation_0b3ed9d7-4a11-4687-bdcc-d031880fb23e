# 轨迹统计分析功能增强

## 概述
为 `test_goal_triggered_recording.sh` 脚本添加了详细的轨迹统计分析功能，能够自动计算无人机和载荷的平均速度、平均加速度以及运动长度等关键指标。

## 新增功能

### ✅ 详细统计分析
脚本现在会自动分析生成的轨迹文件并提供以下统计信息：

#### 速度统计
- **平均速度**: 整个飞行过程中的平均速度
- **最大速度**: 飞行过程中达到的最高速度

#### 加速度统计  
- **平均加速度**: 整个飞行过程中的平均加速度
- **最大加速度**: 飞行过程中达到的最大加速度

#### 距离统计
- **总行程距离**: 实际飞行的累计距离
- **直线距离**: 起点到终点的直线距离
- **路径效率**: 直线距离与总行程距离的比值

#### 其他统计
- **飞行时长**: 总的飞行时间
- **数据采样率**: 数据记录频率
- **起始和结束位置**: 精确的坐标信息

## 技术实现

### 新增文件
- **`analyze_trajectory_stats.py`**: 专门的轨迹分析脚本
  - 使用pandas和numpy进行数据分析
  - 计算速度和加速度的统计量
  - 计算累积距离和路径效率
  - 提供详细的格式化输出

### 脚本集成
- 修改了 `test_goal_triggered_recording.sh`
- 在轨迹文件生成后自动调用分析脚本
- 提供完整的统计报告

## 使用方法

### 自动分析 (推荐)
```bash
cd /home/<USER>/impactor_ws
./test_goal_triggered_recording.sh
```
脚本会在任务完成后自动显示详细的统计分析结果。

### 手动分析
```bash
# 分析特定的轨迹文件
python3 analyze_trajectory_stats.py trajectory_log_20250801_191437.csv

# 简洁输出模式
python3 analyze_trajectory_stats.py trajectory_log_20250801_191437.csv --quiet
```

## 输出示例

### 完整分析报告
```
============================================================
TRAJECTORY STATISTICS ANALYSIS
============================================================
File: trajectory_log_20250801_191437.csv
Duration: 15.97 seconds
Data Points: 884
Sampling Rate: 55.3 Hz

------------------------------ QUADROTOR ------------------------------
Start Position: (-5.997, -0.004, 1.999) m
End Position:   (6.000, -1.000, 2.000) m

Average Velocity:     0.884 m/s
Maximum Velocity:     1.776 m/s
Average Acceleration: 1.125 m/s²
Maximum Acceleration: 3.861 m/s²

Total Travel Distance:    14.133 m
Straight-line Distance:   12.038 m
Path Efficiency:          85.2%

-------------------------------- LOAD --------------------------------
Start Position: (-6.000, -0.001, 1.360) m
End Position:   (6.000, -1.000, 1.356) m

Average Velocity:     1.078 m/s
Maximum Velocity:     3.401 m/s
Average Acceleration: 2.891 m/s²
Maximum Acceleration: 13.252 m/s²

Total Travel Distance:    17.228 m
Straight-line Distance:   12.041 m
Path Efficiency:          69.9%

---------------------------- SUMMARY ----------------------------
System completed 12.0m mission in 16.0s
Average system velocity: 0.98 m/s
Quadrotor-Load distance: 0.644 m
============================================================
```

## 关键指标解释

### 路径效率 (Path Efficiency)
- **定义**: 直线距离与实际飞行距离的比值
- **意义**: 反映轨迹规划的优化程度
- **范围**: 0-100%，越高表示路径越直接

### 平均vs最大值
- **平均值**: 反映整体性能水平
- **最大值**: 反映系统的峰值能力

### 四旋翼vs载荷
- **四旋翼**: 通常路径效率更高，运动更平稳
- **载荷**: 由于惯性和摆动，可能有更大的加速度变化

## 应用价值

### 性能评估
- 评估不同控制算法的性能
- 比较不同参数设置的效果
- 分析系统的稳定性和效率

### 算法优化
- 识别需要改进的性能指标
- 验证优化效果
- 提供量化的改进依据

### 研究分析
- 提供详细的实验数据
- 支持学术研究和论文写作
- 便于结果对比和分析

## 总结

这个增强功能为IMPACTOR系统提供了强大的数据分析能力：

✅ **自动化**: 无需手动计算，自动生成详细报告  
✅ **全面性**: 涵盖速度、加速度、距离等关键指标  
✅ **易用性**: 集成到现有脚本中，使用简单  
✅ **专业性**: 提供格式化的专业分析报告  
✅ **可扩展**: 易于添加新的分析指标  

这些统计数据对于系统性能评估、算法优化和研究分析都具有重要价值。
