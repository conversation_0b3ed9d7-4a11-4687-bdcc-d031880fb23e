/usr/bin/c++ -fPIC -O3 -Wall  -shared -Wl,-soname,libso3_controller_nodelet.so -o /home/<USER>/impactor_ws/devel/lib/libso3_controller_nodelet.so CMakeFiles/so3_controller_nodelet.dir/src/so3_controller_nodelet.cpp.o  -Wl,-rpath,/home/<USER>/impactor_ws/devel/lib:/opt/ros/noetic/lib /home/<USER>/impactor_ws/devel/lib/libencode_msgs.so /home/<USER>/impactor_ws/devel/lib/libdecode_msgs.so /opt/ros/noetic/lib/libnodeletlib.so /opt/ros/noetic/lib/libbondcpp.so -luuid /opt/ros/noetic/lib/libclass_loader.so -lPocoFoundation -ldl /opt/ros/noetic/lib/libroslib.so /opt/ros/noetic/lib/librospack.so -lpython3.8 /usr/lib/x86_64-linux-gnu/libboost_program_options.so.1.71.0 -ltinyxml2 /opt/ros/noetic/lib/libroscpp.so -lpthread /usr/lib/x86_64-linux-gnu/libboost_chrono.so.1.71.0 /usr/lib/x86_64-linux-gnu/libboost_filesystem.so.1.71.0 /opt/ros/noetic/lib/librosconsole.so /opt/ros/noetic/lib/librosconsole_log4cxx.so /opt/ros/noetic/lib/librosconsole_backend_interface.so -llog4cxx /usr/lib/x86_64-linux-gnu/libboost_regex.so.1.71.0 /opt/ros/noetic/lib/libxmlrpcpp.so /opt/ros/noetic/lib/libroscpp_serialization.so /opt/ros/noetic/lib/librostime.so /usr/lib/x86_64-linux-gnu/libboost_date_time.so.1.71.0 /opt/ros/noetic/lib/libcpp_common.so /usr/lib/x86_64-linux-gnu/libboost_system.so.1.71.0 /usr/lib/x86_64-linux-gnu/libboost_thread.so.1.71.0 /usr/lib/x86_64-linux-gnu/libconsole_bridge.so.0.4 
