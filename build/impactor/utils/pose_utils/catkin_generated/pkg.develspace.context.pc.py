# generated from catkin/cmake/template/pkg.context.pc.in
CATKIN_PACKAGE_PREFIX = ""
PROJECT_PKG_CONFIG_INCLUDE_DIRS = "/home/<USER>/impactor_ws/src/impactor/utils/pose_utils/include".split(';') if "/home/<USER>/impactor_ws/src/impactor/utils/pose_utils/include" != "" else []
PROJECT_CATKIN_DEPENDS = "".replace(';', ' ')
PKG_CONFIG_LIBRARIES_WITH_PREFIX = "-lpose_utils".split(';') if "-lpose_utils" != "" else []
PROJECT_NAME = "pose_utils"
PROJECT_SPACE_DIR = "/home/<USER>/impactor_ws/devel"
PROJECT_VERSION = "0.0.0"
