#IncludeRegexLine: ^[ 	]*[#%][ 	]*(include|import)[ 	]*[<"]([^">]+)([">])

#IncludeRegexScan: ^.*$

#IncludeRegexComplain: ^$

#IncludeRegexTransform: 

/home/<USER>/impactor_ws/src/impactor/utils/pose_utils/include/pose_utils.h
iostream
-
armadillo
/home/<USER>/impactor_ws/src/impactor/utils/pose_utils/include/armadillo

/home/<USER>/impactor_ws/src/impactor/utils/pose_utils/src/pose_utils.cpp
pose_utils.h
/home/<USER>/impactor_ws/src/impactor/utils/pose_utils/src/pose_utils.h

