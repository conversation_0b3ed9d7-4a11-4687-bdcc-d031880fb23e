# generated from catkin/cmake/template/pkg.context.pc.in
CATKIN_PACKAGE_PREFIX = ""
PROJECT_PKG_CONFIG_INCLUDE_DIRS = "${prefix}/include".split(';') if "${prefix}/include" != "" else []
PROJECT_CATKIN_DEPENDS = "".replace(';', ' ')
PKG_CONFIG_LIBRARIES_WITH_PREFIX = "-lenv_generator".split(';') if "-lenv_generator" != "" else []
PROJECT_NAME = "env_generator"
PROJECT_SPACE_DIR = "/home/<USER>/impactor_ws/install"
PROJECT_VERSION = "0.0.0"
