<package>
  <version>0.0.0</version>
  <name>odom_visualization</name>
  <description>

     odom_visualization

  </description>
  <maintainer email="eesh<PERSON><PERSON><PERSON>@todo.todo"><PERSON><PERSON><PERSON><PERSON></maintainer>
  <license>BSD</license>
  <url>http://ros.org/wiki/odom_visualization</url>

  <buildtool_depend>catkin</buildtool_depend>

  <build_depend>roscpp</build_depend>
  <build_depend>sensor_msgs</build_depend>
  <build_depend>nav_msgs</build_depend>
  <build_depend>visualization_msgs</build_depend>
  <build_depend>tf</build_depend>
  <build_depend>pose_utils</build_depend>

  <run_depend>roscpp</run_depend>
  <run_depend>sensor_msgs</run_depend>
  <run_depend>nav_msgs</run_depend>
  <run_depend>visualization_msgs</run_depend>
  <run_depend>tf</run_depend>
  <run_depend>pose_utils</run_depend>

</package>


