set(_CATKIN_CURRENT_PACKAGE "odom_visualization")
set(odom_visualization_VERSION "0.0.0")
set(odom_visualization_MAINTAINER "<PERSON><PERSON><PERSON><PERSON> <e<PERSON><PERSON><PERSON><PERSON>@todo.todo>")
set(odom_visualization_PACKAGE_FORMAT "1")
set(odom_visualization_BUILD_DEPENDS "roscpp" "sensor_msgs" "nav_msgs" "visualization_msgs" "tf" "pose_utils")
set(odom_visualization_BUILD_EXPORT_DEPENDS "roscpp" "sensor_msgs" "nav_msgs" "visualization_msgs" "tf" "pose_utils")
set(odom_visualization_BUILDTOOL_DEPENDS "catkin")
set(odom_visualization_BUILDTOOL_EXPORT_DEPENDS )
set(odom_visualization_EXEC_DEPENDS "roscpp" "sensor_msgs" "nav_msgs" "visualization_msgs" "tf" "pose_utils")
set(odom_visualization_RUN_DEPENDS "roscpp" "sensor_msgs" "nav_msgs" "visualization_msgs" "tf" "pose_utils")
set(odom_visualization_TEST_DEPENDS )
set(odom_visualization_DOC_DEPENDS )
set(odom_visualization_URL_WEBSITE "http://ros.org/wiki/odom_visualization")
set(odom_visualization_URL_BUGTRACKER "")
set(odom_visualization_URL_REPOSITORY "")
set(odom_visualization_DEPRECATED "")