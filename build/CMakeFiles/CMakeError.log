Performing C SOURCE FILE Test CMAKE_HAVE_LIBC_PTHREAD failed with the following output:
Change Dir: /home/<USER>/impactor_ws/build/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/make cmTC_8c20a/fast && /usr/bin/make -f CMakeFiles/cmTC_8c20a.dir/build.make CMakeFiles/cmTC_8c20a.dir/build
make[1]: 进入目录“/home/<USER>/impactor_ws/build/CMakeFiles/CMakeTmp”
Building C object CMakeFiles/cmTC_8c20a.dir/src.c.o
/usr/bin/cc   -DCMAKE_HAVE_LIBC_PTHREAD   -o CMakeFiles/cmTC_8c20a.dir/src.c.o   -c /home/<USER>/impactor_ws/build/CMakeFiles/CMakeTmp/src.c
Linking C executable cmTC_8c20a
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_8c20a.dir/link.txt --verbose=1
/usr/bin/cc  -DCMAKE_HAVE_LIBC_PTHREAD    -rdynamic CMakeFiles/cmTC_8c20a.dir/src.c.o  -o cmTC_8c20a 
/usr/bin/ld: CMakeFiles/cmTC_8c20a.dir/src.c.o: in function `main':
src.c:(.text+0x46): undefined reference to `pthread_create'
/usr/bin/ld: src.c:(.text+0x52): undefined reference to `pthread_detach'
/usr/bin/ld: src.c:(.text+0x63): undefined reference to `pthread_join'
collect2: error: ld returned 1 exit status
make[1]: *** [CMakeFiles/cmTC_8c20a.dir/build.make:87：cmTC_8c20a] 错误 1
make[1]: 离开目录“/home/<USER>/impactor_ws/build/CMakeFiles/CMakeTmp”
make: *** [Makefile:121：cmTC_8c20a/fast] 错误 2


Source file was:
#include <pthread.h>

void* test_func(void* data)
{
  return data;
}

int main(void)
{
  pthread_t thread;
  pthread_create(&thread, NULL, test_func, NULL);
  pthread_detach(thread);
  pthread_join(thread, NULL);
  pthread_atfork(NULL, NULL, NULL);
  pthread_exit(NULL);

  return 0;
}

Determining if the function pthread_create exists in the pthreads failed with the following output:
Change Dir: /home/<USER>/impactor_ws/build/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/make cmTC_60c81/fast && /usr/bin/make -f CMakeFiles/cmTC_60c81.dir/build.make CMakeFiles/cmTC_60c81.dir/build
make[1]: 进入目录“/home/<USER>/impactor_ws/build/CMakeFiles/CMakeTmp”
Building C object CMakeFiles/cmTC_60c81.dir/CheckFunctionExists.c.o
/usr/bin/cc   -DCHECK_FUNCTION_EXISTS=pthread_create   -o CMakeFiles/cmTC_60c81.dir/CheckFunctionExists.c.o   -c /usr/share/cmake-3.16/Modules/CheckFunctionExists.c
Linking C executable cmTC_60c81
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_60c81.dir/link.txt --verbose=1
/usr/bin/cc  -DCHECK_FUNCTION_EXISTS=pthread_create    -rdynamic CMakeFiles/cmTC_60c81.dir/CheckFunctionExists.c.o  -o cmTC_60c81  -lpthreads 
/usr/bin/ld: 找不到 -lpthreads
collect2: error: ld returned 1 exit status
make[1]: *** [CMakeFiles/cmTC_60c81.dir/build.make:87：cmTC_60c81] 错误 1
make[1]: 离开目录“/home/<USER>/impactor_ws/build/CMakeFiles/CMakeTmp”
make: *** [Makefile:121：cmTC_60c81/fast] 错误 2



