// Generated by gencpp from file quadrotor_msgs/Odometry.msg
// DO NOT EDIT!


#ifndef QUADROTOR_MSGS_MESSAGE_ODOMETRY_H
#define QUADROTOR_MSGS_MESSAGE_ODOMETRY_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>

#include <nav_msgs/Odometry.h>
#include <nav_msgs/Odometry.h>

namespace quadrotor_msgs
{
template <class ContainerAllocator>
struct Odometry_
{
  typedef Odometry_<ContainerAllocator> Type;

  Odometry_()
    : curodom()
    , kfodom()
    , kfid(0)
    , status(0)  {
    }
  Odometry_(const ContainerAllocator& _alloc)
    : curodom(_alloc)
    , kfodom(_alloc)
    , kfid(0)
    , status(0)  {
  (void)_alloc;
    }



   typedef  ::nav_msgs::Odometry_<ContainerAllocator>  _curodom_type;
  _curodom_type curodom;

   typedef  ::nav_msgs::Odometry_<ContainerAllocator>  _kfodom_type;
  _kfodom_type kfodom;

   typedef uint32_t _kfid_type;
  _kfid_type kfid;

   typedef uint8_t _status_type;
  _status_type status;



// reducing the odds to have name collisions with Windows.h 
#if defined(_WIN32) && defined(STATUS_ODOM_VALID)
  #undef STATUS_ODOM_VALID
#endif
#if defined(_WIN32) && defined(STATUS_ODOM_INVALID)
  #undef STATUS_ODOM_INVALID
#endif
#if defined(_WIN32) && defined(STATUS_ODOM_LOOPCLOSURE)
  #undef STATUS_ODOM_LOOPCLOSURE
#endif

  enum {
    STATUS_ODOM_VALID = 0u,
    STATUS_ODOM_INVALID = 1u,
    STATUS_ODOM_LOOPCLOSURE = 2u,
  };


  typedef boost::shared_ptr< ::quadrotor_msgs::Odometry_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::quadrotor_msgs::Odometry_<ContainerAllocator> const> ConstPtr;

}; // struct Odometry_

typedef ::quadrotor_msgs::Odometry_<std::allocator<void> > Odometry;

typedef boost::shared_ptr< ::quadrotor_msgs::Odometry > OdometryPtr;
typedef boost::shared_ptr< ::quadrotor_msgs::Odometry const> OdometryConstPtr;

// constants requiring out of line definition

   

   

   



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::quadrotor_msgs::Odometry_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::quadrotor_msgs::Odometry_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::quadrotor_msgs::Odometry_<ContainerAllocator1> & lhs, const ::quadrotor_msgs::Odometry_<ContainerAllocator2> & rhs)
{
  return lhs.curodom == rhs.curodom &&
    lhs.kfodom == rhs.kfodom &&
    lhs.kfid == rhs.kfid &&
    lhs.status == rhs.status;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::quadrotor_msgs::Odometry_<ContainerAllocator1> & lhs, const ::quadrotor_msgs::Odometry_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace quadrotor_msgs

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::quadrotor_msgs::Odometry_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::quadrotor_msgs::Odometry_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::quadrotor_msgs::Odometry_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::quadrotor_msgs::Odometry_<ContainerAllocator> const>
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::quadrotor_msgs::Odometry_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::quadrotor_msgs::Odometry_<ContainerAllocator> const>
  : FalseType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::quadrotor_msgs::Odometry_<ContainerAllocator> >
{
  static const char* value()
  {
    return "94d99f86002b25504a5d3354fa1ad709";
  }

  static const char* value(const ::quadrotor_msgs::Odometry_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0x94d99f86002b2550ULL;
  static const uint64_t static_value2 = 0x4a5d3354fa1ad709ULL;
};

template<class ContainerAllocator>
struct DataType< ::quadrotor_msgs::Odometry_<ContainerAllocator> >
{
  static const char* value()
  {
    return "quadrotor_msgs/Odometry";
  }

  static const char* value(const ::quadrotor_msgs::Odometry_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::quadrotor_msgs::Odometry_<ContainerAllocator> >
{
  static const char* value()
  {
    return "uint8 STATUS_ODOM_VALID=0\n"
"uint8 STATUS_ODOM_INVALID=1\n"
"uint8 STATUS_ODOM_LOOPCLOSURE=2\n"
"\n"
"nav_msgs/Odometry curodom\n"
"nav_msgs/Odometry kfodom\n"
"uint32 kfid\n"
"uint8 status\n"
"\n"
"================================================================================\n"
"MSG: nav_msgs/Odometry\n"
"# This represents an estimate of a position and velocity in free space.  \n"
"# The pose in this message should be specified in the coordinate frame given by header.frame_id.\n"
"# The twist in this message should be specified in the coordinate frame given by the child_frame_id\n"
"Header header\n"
"string child_frame_id\n"
"geometry_msgs/PoseWithCovariance pose\n"
"geometry_msgs/TwistWithCovariance twist\n"
"\n"
"================================================================================\n"
"MSG: std_msgs/Header\n"
"# Standard metadata for higher-level stamped data types.\n"
"# This is generally used to communicate timestamped data \n"
"# in a particular coordinate frame.\n"
"# \n"
"# sequence ID: consecutively increasing ID \n"
"uint32 seq\n"
"#Two-integer timestamp that is expressed as:\n"
"# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')\n"
"# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')\n"
"# time-handling sugar is provided by the client library\n"
"time stamp\n"
"#Frame this data is associated with\n"
"string frame_id\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/PoseWithCovariance\n"
"# This represents a pose in free space with uncertainty.\n"
"\n"
"Pose pose\n"
"\n"
"# Row-major representation of the 6x6 covariance matrix\n"
"# The orientation parameters use a fixed-axis representation.\n"
"# In order, the parameters are:\n"
"# (x, y, z, rotation about X axis, rotation about Y axis, rotation about Z axis)\n"
"float64[36] covariance\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Pose\n"
"# A representation of pose in free space, composed of position and orientation. \n"
"Point position\n"
"Quaternion orientation\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Point\n"
"# This contains the position of a point in free space\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Quaternion\n"
"# This represents an orientation in free space in quaternion form.\n"
"\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
"float64 w\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/TwistWithCovariance\n"
"# This expresses velocity in free space with uncertainty.\n"
"\n"
"Twist twist\n"
"\n"
"# Row-major representation of the 6x6 covariance matrix\n"
"# The orientation parameters use a fixed-axis representation.\n"
"# In order, the parameters are:\n"
"# (x, y, z, rotation about X axis, rotation about Y axis, rotation about Z axis)\n"
"float64[36] covariance\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Twist\n"
"# This expresses velocity in free space broken into its linear and angular parts.\n"
"Vector3 linear\n"
"Vector3 angular\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Vector3\n"
"# This represents a vector in free space. \n"
"# It is only meant to represent a direction. Therefore, it does not\n"
"# make sense to apply a translation to it (e.g., when applying a \n"
"# generic rigid transformation to a Vector3, tf2 will only apply the\n"
"# rotation). If you want your data to be translatable too, use the\n"
"# geometry_msgs/Point message instead.\n"
"\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
;
  }

  static const char* value(const ::quadrotor_msgs::Odometry_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::quadrotor_msgs::Odometry_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.curodom);
      stream.next(m.kfodom);
      stream.next(m.kfid);
      stream.next(m.status);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct Odometry_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::quadrotor_msgs::Odometry_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::quadrotor_msgs::Odometry_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "curodom: ";
    Printer< ::nav_msgs::Odometry_<ContainerAllocator> >::stream(s, indent + "  ", v.curodom);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "kfodom: ";
    Printer< ::nav_msgs::Odometry_<ContainerAllocator> >::stream(s, indent + "  ", v.kfodom);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "kfid: ";
    Printer<uint32_t>::stream(s, indent + "  ", v.kfid);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "status: ";
    Printer<uint8_t>::stream(s, indent + "  ", v.status);
  }
};

} // namespace message_operations
} // namespace ros

#endif // QUADROTOR_MSGS_MESSAGE_ODOMETRY_H
