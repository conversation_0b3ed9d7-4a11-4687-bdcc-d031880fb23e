// Generated by gencpp from file quadrotor_msgs/Gains.msg
// DO NOT EDIT!


#ifndef QUADROTOR_MSGS_MESSAGE_GAINS_H
#define QUADROTOR_MSGS_MESSAGE_GAINS_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>


namespace quadrotor_msgs
{
template <class ContainerAllocator>
struct Gains_
{
  typedef Gains_<ContainerAllocator> Type;

  Gains_()
    : Kp(0.0)
    , Kd(0.0)
    , Kp_yaw(0.0)
    , Kd_yaw(0.0)  {
    }
  Gains_(const ContainerAllocator& _alloc)
    : Kp(0.0)
    , Kd(0.0)
    , Kp_yaw(0.0)
    , Kd_yaw(0.0)  {
  (void)_alloc;
    }



   typedef double _Kp_type;
  _Kp_type Kp;

   typedef double _Kd_type;
  _Kd_type Kd;

   typedef double _Kp_yaw_type;
  _Kp_yaw_type Kp_yaw;

   typedef double _Kd_yaw_type;
  _Kd_yaw_type Kd_yaw;





  typedef boost::shared_ptr< ::quadrotor_msgs::Gains_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::quadrotor_msgs::Gains_<ContainerAllocator> const> ConstPtr;

}; // struct Gains_

typedef ::quadrotor_msgs::Gains_<std::allocator<void> > Gains;

typedef boost::shared_ptr< ::quadrotor_msgs::Gains > GainsPtr;
typedef boost::shared_ptr< ::quadrotor_msgs::Gains const> GainsConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::quadrotor_msgs::Gains_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::quadrotor_msgs::Gains_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::quadrotor_msgs::Gains_<ContainerAllocator1> & lhs, const ::quadrotor_msgs::Gains_<ContainerAllocator2> & rhs)
{
  return lhs.Kp == rhs.Kp &&
    lhs.Kd == rhs.Kd &&
    lhs.Kp_yaw == rhs.Kp_yaw &&
    lhs.Kd_yaw == rhs.Kd_yaw;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::quadrotor_msgs::Gains_<ContainerAllocator1> & lhs, const ::quadrotor_msgs::Gains_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace quadrotor_msgs

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::quadrotor_msgs::Gains_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::quadrotor_msgs::Gains_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::quadrotor_msgs::Gains_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::quadrotor_msgs::Gains_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct HasHeader< ::quadrotor_msgs::Gains_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::quadrotor_msgs::Gains_<ContainerAllocator> const>
  : FalseType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::quadrotor_msgs::Gains_<ContainerAllocator> >
{
  static const char* value()
  {
    return "b82763b9f24e5595e2a816aa779c9461";
  }

  static const char* value(const ::quadrotor_msgs::Gains_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0xb82763b9f24e5595ULL;
  static const uint64_t static_value2 = 0xe2a816aa779c9461ULL;
};

template<class ContainerAllocator>
struct DataType< ::quadrotor_msgs::Gains_<ContainerAllocator> >
{
  static const char* value()
  {
    return "quadrotor_msgs/Gains";
  }

  static const char* value(const ::quadrotor_msgs::Gains_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::quadrotor_msgs::Gains_<ContainerAllocator> >
{
  static const char* value()
  {
    return "float64 Kp\n"
"float64 Kd\n"
"float64 Kp_yaw\n"
"float64 Kd_yaw\n"
;
  }

  static const char* value(const ::quadrotor_msgs::Gains_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::quadrotor_msgs::Gains_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.Kp);
      stream.next(m.Kd);
      stream.next(m.Kp_yaw);
      stream.next(m.Kd_yaw);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct Gains_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::quadrotor_msgs::Gains_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::quadrotor_msgs::Gains_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "Kp: ";
    Printer<double>::stream(s, indent + "  ", v.Kp);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "Kd: ";
    Printer<double>::stream(s, indent + "  ", v.Kd);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "Kp_yaw: ";
    Printer<double>::stream(s, indent + "  ", v.Kp_yaw);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "Kd_yaw: ";
    Printer<double>::stream(s, indent + "  ", v.Kd_yaw);
  }
};

} // namespace message_operations
} // namespace ros

#endif // QUADROTOR_MSGS_MESSAGE_GAINS_H
