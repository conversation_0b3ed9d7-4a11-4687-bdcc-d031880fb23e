# This Python file uses the following encoding: utf-8
"""autogenerated by genpy from quadrotor_msgs/Odometry.msg. Do not edit."""
import codecs
import sys
python3 = True if sys.hexversion > 0x03000000 else False
import genpy
import struct

import geometry_msgs.msg
import nav_msgs.msg
import std_msgs.msg

class Odometry(genpy.Message):
  _md5sum = "94d99f86002b25504a5d3354fa1ad709"
  _type = "quadrotor_msgs/Odometry"
  _has_header = False  # flag to mark the presence of a Header object
  _full_text = """uint8 STATUS_ODOM_VALID=0
uint8 STATUS_ODOM_INVALID=1
uint8 STATUS_ODOM_LOOPCLOSURE=2

nav_msgs/Odometry curodom
nav_msgs/Odometry kfodom
uint32 kfid
uint8 status

================================================================================
MSG: nav_msgs/Odometry
# This represents an estimate of a position and velocity in free space.  
# The pose in this message should be specified in the coordinate frame given by header.frame_id.
# The twist in this message should be specified in the coordinate frame given by the child_frame_id
Header header
string child_frame_id
geometry_msgs/PoseWithCovariance pose
geometry_msgs/TwistWithCovariance twist

================================================================================
MSG: std_msgs/Header
# Standard metadata for higher-level stamped data types.
# This is generally used to communicate timestamped data 
# in a particular coordinate frame.
# 
# sequence ID: consecutively increasing ID 
uint32 seq
#Two-integer timestamp that is expressed as:
# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')
# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')
# time-handling sugar is provided by the client library
time stamp
#Frame this data is associated with
string frame_id

================================================================================
MSG: geometry_msgs/PoseWithCovariance
# This represents a pose in free space with uncertainty.

Pose pose

# Row-major representation of the 6x6 covariance matrix
# The orientation parameters use a fixed-axis representation.
# In order, the parameters are:
# (x, y, z, rotation about X axis, rotation about Y axis, rotation about Z axis)
float64[36] covariance

================================================================================
MSG: geometry_msgs/Pose
# A representation of pose in free space, composed of position and orientation. 
Point position
Quaternion orientation

================================================================================
MSG: geometry_msgs/Point
# This contains the position of a point in free space
float64 x
float64 y
float64 z

================================================================================
MSG: geometry_msgs/Quaternion
# This represents an orientation in free space in quaternion form.

float64 x
float64 y
float64 z
float64 w

================================================================================
MSG: geometry_msgs/TwistWithCovariance
# This expresses velocity in free space with uncertainty.

Twist twist

# Row-major representation of the 6x6 covariance matrix
# The orientation parameters use a fixed-axis representation.
# In order, the parameters are:
# (x, y, z, rotation about X axis, rotation about Y axis, rotation about Z axis)
float64[36] covariance

================================================================================
MSG: geometry_msgs/Twist
# This expresses velocity in free space broken into its linear and angular parts.
Vector3 linear
Vector3 angular

================================================================================
MSG: geometry_msgs/Vector3
# This represents a vector in free space. 
# It is only meant to represent a direction. Therefore, it does not
# make sense to apply a translation to it (e.g., when applying a 
# generic rigid transformation to a Vector3, tf2 will only apply the
# rotation). If you want your data to be translatable too, use the
# geometry_msgs/Point message instead.

float64 x
float64 y
float64 z"""
  # Pseudo-constants
  STATUS_ODOM_VALID = 0
  STATUS_ODOM_INVALID = 1
  STATUS_ODOM_LOOPCLOSURE = 2

  __slots__ = ['curodom','kfodom','kfid','status']
  _slot_types = ['nav_msgs/Odometry','nav_msgs/Odometry','uint32','uint8']

  def __init__(self, *args, **kwds):
    """
    Constructor. Any message fields that are implicitly/explicitly
    set to None will be assigned a default value. The recommend
    use is keyword arguments as this is more robust to future message
    changes.  You cannot mix in-order arguments and keyword arguments.

    The available fields are:
       curodom,kfodom,kfid,status

    :param args: complete set of field values, in .msg order
    :param kwds: use keyword arguments corresponding to message field names
    to set specific fields.
    """
    if args or kwds:
      super(Odometry, self).__init__(*args, **kwds)
      # message fields cannot be None, assign default values for those that are
      if self.curodom is None:
        self.curodom = nav_msgs.msg.Odometry()
      if self.kfodom is None:
        self.kfodom = nav_msgs.msg.Odometry()
      if self.kfid is None:
        self.kfid = 0
      if self.status is None:
        self.status = 0
    else:
      self.curodom = nav_msgs.msg.Odometry()
      self.kfodom = nav_msgs.msg.Odometry()
      self.kfid = 0
      self.status = 0

  def _get_types(self):
    """
    internal API method
    """
    return self._slot_types

  def serialize(self, buff):
    """
    serialize message into buffer
    :param buff: buffer, ``StringIO``
    """
    try:
      _x = self
      buff.write(_get_struct_3I().pack(_x.curodom.header.seq, _x.curodom.header.stamp.secs, _x.curodom.header.stamp.nsecs))
      _x = self.curodom.header.frame_id
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self.curodom.child_frame_id
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self
      buff.write(_get_struct_7d().pack(_x.curodom.pose.pose.position.x, _x.curodom.pose.pose.position.y, _x.curodom.pose.pose.position.z, _x.curodom.pose.pose.orientation.x, _x.curodom.pose.pose.orientation.y, _x.curodom.pose.pose.orientation.z, _x.curodom.pose.pose.orientation.w))
      buff.write(_get_struct_36d().pack(*self.curodom.pose.covariance))
      _x = self
      buff.write(_get_struct_6d().pack(_x.curodom.twist.twist.linear.x, _x.curodom.twist.twist.linear.y, _x.curodom.twist.twist.linear.z, _x.curodom.twist.twist.angular.x, _x.curodom.twist.twist.angular.y, _x.curodom.twist.twist.angular.z))
      buff.write(_get_struct_36d().pack(*self.curodom.twist.covariance))
      _x = self
      buff.write(_get_struct_3I().pack(_x.kfodom.header.seq, _x.kfodom.header.stamp.secs, _x.kfodom.header.stamp.nsecs))
      _x = self.kfodom.header.frame_id
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self.kfodom.child_frame_id
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self
      buff.write(_get_struct_7d().pack(_x.kfodom.pose.pose.position.x, _x.kfodom.pose.pose.position.y, _x.kfodom.pose.pose.position.z, _x.kfodom.pose.pose.orientation.x, _x.kfodom.pose.pose.orientation.y, _x.kfodom.pose.pose.orientation.z, _x.kfodom.pose.pose.orientation.w))
      buff.write(_get_struct_36d().pack(*self.kfodom.pose.covariance))
      _x = self
      buff.write(_get_struct_6d().pack(_x.kfodom.twist.twist.linear.x, _x.kfodom.twist.twist.linear.y, _x.kfodom.twist.twist.linear.z, _x.kfodom.twist.twist.angular.x, _x.kfodom.twist.twist.angular.y, _x.kfodom.twist.twist.angular.z))
      buff.write(_get_struct_36d().pack(*self.kfodom.twist.covariance))
      _x = self
      buff.write(_get_struct_IB().pack(_x.kfid, _x.status))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize(self, str):
    """
    unpack serialized message in str into this message instance
    :param str: byte array of serialized message, ``str``
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      if self.curodom is None:
        self.curodom = nav_msgs.msg.Odometry()
      if self.kfodom is None:
        self.kfodom = nav_msgs.msg.Odometry()
      end = 0
      _x = self
      start = end
      end += 12
      (_x.curodom.header.seq, _x.curodom.header.stamp.secs, _x.curodom.header.stamp.nsecs,) = _get_struct_3I().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.curodom.header.frame_id = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.curodom.header.frame_id = str[start:end]
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.curodom.child_frame_id = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.curodom.child_frame_id = str[start:end]
      _x = self
      start = end
      end += 56
      (_x.curodom.pose.pose.position.x, _x.curodom.pose.pose.position.y, _x.curodom.pose.pose.position.z, _x.curodom.pose.pose.orientation.x, _x.curodom.pose.pose.orientation.y, _x.curodom.pose.pose.orientation.z, _x.curodom.pose.pose.orientation.w,) = _get_struct_7d().unpack(str[start:end])
      start = end
      end += 288
      self.curodom.pose.covariance = _get_struct_36d().unpack(str[start:end])
      _x = self
      start = end
      end += 48
      (_x.curodom.twist.twist.linear.x, _x.curodom.twist.twist.linear.y, _x.curodom.twist.twist.linear.z, _x.curodom.twist.twist.angular.x, _x.curodom.twist.twist.angular.y, _x.curodom.twist.twist.angular.z,) = _get_struct_6d().unpack(str[start:end])
      start = end
      end += 288
      self.curodom.twist.covariance = _get_struct_36d().unpack(str[start:end])
      _x = self
      start = end
      end += 12
      (_x.kfodom.header.seq, _x.kfodom.header.stamp.secs, _x.kfodom.header.stamp.nsecs,) = _get_struct_3I().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.kfodom.header.frame_id = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.kfodom.header.frame_id = str[start:end]
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.kfodom.child_frame_id = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.kfodom.child_frame_id = str[start:end]
      _x = self
      start = end
      end += 56
      (_x.kfodom.pose.pose.position.x, _x.kfodom.pose.pose.position.y, _x.kfodom.pose.pose.position.z, _x.kfodom.pose.pose.orientation.x, _x.kfodom.pose.pose.orientation.y, _x.kfodom.pose.pose.orientation.z, _x.kfodom.pose.pose.orientation.w,) = _get_struct_7d().unpack(str[start:end])
      start = end
      end += 288
      self.kfodom.pose.covariance = _get_struct_36d().unpack(str[start:end])
      _x = self
      start = end
      end += 48
      (_x.kfodom.twist.twist.linear.x, _x.kfodom.twist.twist.linear.y, _x.kfodom.twist.twist.linear.z, _x.kfodom.twist.twist.angular.x, _x.kfodom.twist.twist.angular.y, _x.kfodom.twist.twist.angular.z,) = _get_struct_6d().unpack(str[start:end])
      start = end
      end += 288
      self.kfodom.twist.covariance = _get_struct_36d().unpack(str[start:end])
      _x = self
      start = end
      end += 5
      (_x.kfid, _x.status,) = _get_struct_IB().unpack(str[start:end])
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill


  def serialize_numpy(self, buff, numpy):
    """
    serialize message with numpy array types into buffer
    :param buff: buffer, ``StringIO``
    :param numpy: numpy python module
    """
    try:
      _x = self
      buff.write(_get_struct_3I().pack(_x.curodom.header.seq, _x.curodom.header.stamp.secs, _x.curodom.header.stamp.nsecs))
      _x = self.curodom.header.frame_id
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self.curodom.child_frame_id
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self
      buff.write(_get_struct_7d().pack(_x.curodom.pose.pose.position.x, _x.curodom.pose.pose.position.y, _x.curodom.pose.pose.position.z, _x.curodom.pose.pose.orientation.x, _x.curodom.pose.pose.orientation.y, _x.curodom.pose.pose.orientation.z, _x.curodom.pose.pose.orientation.w))
      buff.write(self.curodom.pose.covariance.tostring())
      _x = self
      buff.write(_get_struct_6d().pack(_x.curodom.twist.twist.linear.x, _x.curodom.twist.twist.linear.y, _x.curodom.twist.twist.linear.z, _x.curodom.twist.twist.angular.x, _x.curodom.twist.twist.angular.y, _x.curodom.twist.twist.angular.z))
      buff.write(self.curodom.twist.covariance.tostring())
      _x = self
      buff.write(_get_struct_3I().pack(_x.kfodom.header.seq, _x.kfodom.header.stamp.secs, _x.kfodom.header.stamp.nsecs))
      _x = self.kfodom.header.frame_id
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self.kfodom.child_frame_id
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self
      buff.write(_get_struct_7d().pack(_x.kfodom.pose.pose.position.x, _x.kfodom.pose.pose.position.y, _x.kfodom.pose.pose.position.z, _x.kfodom.pose.pose.orientation.x, _x.kfodom.pose.pose.orientation.y, _x.kfodom.pose.pose.orientation.z, _x.kfodom.pose.pose.orientation.w))
      buff.write(self.kfodom.pose.covariance.tostring())
      _x = self
      buff.write(_get_struct_6d().pack(_x.kfodom.twist.twist.linear.x, _x.kfodom.twist.twist.linear.y, _x.kfodom.twist.twist.linear.z, _x.kfodom.twist.twist.angular.x, _x.kfodom.twist.twist.angular.y, _x.kfodom.twist.twist.angular.z))
      buff.write(self.kfodom.twist.covariance.tostring())
      _x = self
      buff.write(_get_struct_IB().pack(_x.kfid, _x.status))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize_numpy(self, str, numpy):
    """
    unpack serialized message in str into this message instance using numpy for array types
    :param str: byte array of serialized message, ``str``
    :param numpy: numpy python module
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      if self.curodom is None:
        self.curodom = nav_msgs.msg.Odometry()
      if self.kfodom is None:
        self.kfodom = nav_msgs.msg.Odometry()
      end = 0
      _x = self
      start = end
      end += 12
      (_x.curodom.header.seq, _x.curodom.header.stamp.secs, _x.curodom.header.stamp.nsecs,) = _get_struct_3I().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.curodom.header.frame_id = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.curodom.header.frame_id = str[start:end]
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.curodom.child_frame_id = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.curodom.child_frame_id = str[start:end]
      _x = self
      start = end
      end += 56
      (_x.curodom.pose.pose.position.x, _x.curodom.pose.pose.position.y, _x.curodom.pose.pose.position.z, _x.curodom.pose.pose.orientation.x, _x.curodom.pose.pose.orientation.y, _x.curodom.pose.pose.orientation.z, _x.curodom.pose.pose.orientation.w,) = _get_struct_7d().unpack(str[start:end])
      start = end
      end += 288
      self.curodom.pose.covariance = numpy.frombuffer(str[start:end], dtype=numpy.float64, count=36)
      _x = self
      start = end
      end += 48
      (_x.curodom.twist.twist.linear.x, _x.curodom.twist.twist.linear.y, _x.curodom.twist.twist.linear.z, _x.curodom.twist.twist.angular.x, _x.curodom.twist.twist.angular.y, _x.curodom.twist.twist.angular.z,) = _get_struct_6d().unpack(str[start:end])
      start = end
      end += 288
      self.curodom.twist.covariance = numpy.frombuffer(str[start:end], dtype=numpy.float64, count=36)
      _x = self
      start = end
      end += 12
      (_x.kfodom.header.seq, _x.kfodom.header.stamp.secs, _x.kfodom.header.stamp.nsecs,) = _get_struct_3I().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.kfodom.header.frame_id = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.kfodom.header.frame_id = str[start:end]
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.kfodom.child_frame_id = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.kfodom.child_frame_id = str[start:end]
      _x = self
      start = end
      end += 56
      (_x.kfodom.pose.pose.position.x, _x.kfodom.pose.pose.position.y, _x.kfodom.pose.pose.position.z, _x.kfodom.pose.pose.orientation.x, _x.kfodom.pose.pose.orientation.y, _x.kfodom.pose.pose.orientation.z, _x.kfodom.pose.pose.orientation.w,) = _get_struct_7d().unpack(str[start:end])
      start = end
      end += 288
      self.kfodom.pose.covariance = numpy.frombuffer(str[start:end], dtype=numpy.float64, count=36)
      _x = self
      start = end
      end += 48
      (_x.kfodom.twist.twist.linear.x, _x.kfodom.twist.twist.linear.y, _x.kfodom.twist.twist.linear.z, _x.kfodom.twist.twist.angular.x, _x.kfodom.twist.twist.angular.y, _x.kfodom.twist.twist.angular.z,) = _get_struct_6d().unpack(str[start:end])
      start = end
      end += 288
      self.kfodom.twist.covariance = numpy.frombuffer(str[start:end], dtype=numpy.float64, count=36)
      _x = self
      start = end
      end += 5
      (_x.kfid, _x.status,) = _get_struct_IB().unpack(str[start:end])
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill

_struct_I = genpy.struct_I
def _get_struct_I():
    global _struct_I
    return _struct_I
_struct_36d = None
def _get_struct_36d():
    global _struct_36d
    if _struct_36d is None:
        _struct_36d = struct.Struct("<36d")
    return _struct_36d
_struct_3I = None
def _get_struct_3I():
    global _struct_3I
    if _struct_3I is None:
        _struct_3I = struct.Struct("<3I")
    return _struct_3I
_struct_6d = None
def _get_struct_6d():
    global _struct_6d
    if _struct_6d is None:
        _struct_6d = struct.Struct("<6d")
    return _struct_6d
_struct_7d = None
def _get_struct_7d():
    global _struct_7d
    if _struct_7d is None:
        _struct_7d = struct.Struct("<7d")
    return _struct_7d
_struct_IB = None
def _get_struct_IB():
    global _struct_IB
    if _struct_IB is None:
        _struct_IB = struct.Struct("<IB")
    return _struct_IB
