# This Python file uses the following encoding: utf-8
"""autogenerated by genpy from quadrotor_msgs/Px4ctrlDebug.msg. Do not edit."""
import codecs
import sys
python3 = True if sys.hexversion > 0x03000000 else False
import genpy
import struct

import std_msgs.msg

class Px4ctrlDebug(genpy.Message):
  _md5sum = "8adafd9362e5a8fc6c85e6f460fbfc53"
  _type = "quadrotor_msgs/Px4ctrlDebug"
  _has_header = True  # flag to mark the presence of a Header object
  _full_text = """Header header

float64 des_v_x
float64 des_v_y
float64 des_v_z

float64 fb_a_x
float64 fb_a_y
float64 fb_a_z

float64 des_a_x
float64 des_a_y
float64 des_a_z

float64 des_q_x
float64 des_q_y
float64 des_q_z
float64 des_q_w

float64 des_thr
float64 hover_percentage
float64 thr_scale_compensate
float64 voltage

float64 err_axisang_x
float64 err_axisang_y
float64 err_axisang_z
float64 err_axisang_ang

float64 fb_rate_x
float64 fb_rate_y
float64 fb_rate_z


================================================================================
MSG: std_msgs/Header
# Standard metadata for higher-level stamped data types.
# This is generally used to communicate timestamped data 
# in a particular coordinate frame.
# 
# sequence ID: consecutively increasing ID 
uint32 seq
#Two-integer timestamp that is expressed as:
# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')
# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')
# time-handling sugar is provided by the client library
time stamp
#Frame this data is associated with
string frame_id
"""
  __slots__ = ['header','des_v_x','des_v_y','des_v_z','fb_a_x','fb_a_y','fb_a_z','des_a_x','des_a_y','des_a_z','des_q_x','des_q_y','des_q_z','des_q_w','des_thr','hover_percentage','thr_scale_compensate','voltage','err_axisang_x','err_axisang_y','err_axisang_z','err_axisang_ang','fb_rate_x','fb_rate_y','fb_rate_z']
  _slot_types = ['std_msgs/Header','float64','float64','float64','float64','float64','float64','float64','float64','float64','float64','float64','float64','float64','float64','float64','float64','float64','float64','float64','float64','float64','float64','float64','float64']

  def __init__(self, *args, **kwds):
    """
    Constructor. Any message fields that are implicitly/explicitly
    set to None will be assigned a default value. The recommend
    use is keyword arguments as this is more robust to future message
    changes.  You cannot mix in-order arguments and keyword arguments.

    The available fields are:
       header,des_v_x,des_v_y,des_v_z,fb_a_x,fb_a_y,fb_a_z,des_a_x,des_a_y,des_a_z,des_q_x,des_q_y,des_q_z,des_q_w,des_thr,hover_percentage,thr_scale_compensate,voltage,err_axisang_x,err_axisang_y,err_axisang_z,err_axisang_ang,fb_rate_x,fb_rate_y,fb_rate_z

    :param args: complete set of field values, in .msg order
    :param kwds: use keyword arguments corresponding to message field names
    to set specific fields.
    """
    if args or kwds:
      super(Px4ctrlDebug, self).__init__(*args, **kwds)
      # message fields cannot be None, assign default values for those that are
      if self.header is None:
        self.header = std_msgs.msg.Header()
      if self.des_v_x is None:
        self.des_v_x = 0.
      if self.des_v_y is None:
        self.des_v_y = 0.
      if self.des_v_z is None:
        self.des_v_z = 0.
      if self.fb_a_x is None:
        self.fb_a_x = 0.
      if self.fb_a_y is None:
        self.fb_a_y = 0.
      if self.fb_a_z is None:
        self.fb_a_z = 0.
      if self.des_a_x is None:
        self.des_a_x = 0.
      if self.des_a_y is None:
        self.des_a_y = 0.
      if self.des_a_z is None:
        self.des_a_z = 0.
      if self.des_q_x is None:
        self.des_q_x = 0.
      if self.des_q_y is None:
        self.des_q_y = 0.
      if self.des_q_z is None:
        self.des_q_z = 0.
      if self.des_q_w is None:
        self.des_q_w = 0.
      if self.des_thr is None:
        self.des_thr = 0.
      if self.hover_percentage is None:
        self.hover_percentage = 0.
      if self.thr_scale_compensate is None:
        self.thr_scale_compensate = 0.
      if self.voltage is None:
        self.voltage = 0.
      if self.err_axisang_x is None:
        self.err_axisang_x = 0.
      if self.err_axisang_y is None:
        self.err_axisang_y = 0.
      if self.err_axisang_z is None:
        self.err_axisang_z = 0.
      if self.err_axisang_ang is None:
        self.err_axisang_ang = 0.
      if self.fb_rate_x is None:
        self.fb_rate_x = 0.
      if self.fb_rate_y is None:
        self.fb_rate_y = 0.
      if self.fb_rate_z is None:
        self.fb_rate_z = 0.
    else:
      self.header = std_msgs.msg.Header()
      self.des_v_x = 0.
      self.des_v_y = 0.
      self.des_v_z = 0.
      self.fb_a_x = 0.
      self.fb_a_y = 0.
      self.fb_a_z = 0.
      self.des_a_x = 0.
      self.des_a_y = 0.
      self.des_a_z = 0.
      self.des_q_x = 0.
      self.des_q_y = 0.
      self.des_q_z = 0.
      self.des_q_w = 0.
      self.des_thr = 0.
      self.hover_percentage = 0.
      self.thr_scale_compensate = 0.
      self.voltage = 0.
      self.err_axisang_x = 0.
      self.err_axisang_y = 0.
      self.err_axisang_z = 0.
      self.err_axisang_ang = 0.
      self.fb_rate_x = 0.
      self.fb_rate_y = 0.
      self.fb_rate_z = 0.

  def _get_types(self):
    """
    internal API method
    """
    return self._slot_types

  def serialize(self, buff):
    """
    serialize message into buffer
    :param buff: buffer, ``StringIO``
    """
    try:
      _x = self
      buff.write(_get_struct_3I().pack(_x.header.seq, _x.header.stamp.secs, _x.header.stamp.nsecs))
      _x = self.header.frame_id
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self
      buff.write(_get_struct_24d().pack(_x.des_v_x, _x.des_v_y, _x.des_v_z, _x.fb_a_x, _x.fb_a_y, _x.fb_a_z, _x.des_a_x, _x.des_a_y, _x.des_a_z, _x.des_q_x, _x.des_q_y, _x.des_q_z, _x.des_q_w, _x.des_thr, _x.hover_percentage, _x.thr_scale_compensate, _x.voltage, _x.err_axisang_x, _x.err_axisang_y, _x.err_axisang_z, _x.err_axisang_ang, _x.fb_rate_x, _x.fb_rate_y, _x.fb_rate_z))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize(self, str):
    """
    unpack serialized message in str into this message instance
    :param str: byte array of serialized message, ``str``
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      if self.header is None:
        self.header = std_msgs.msg.Header()
      end = 0
      _x = self
      start = end
      end += 12
      (_x.header.seq, _x.header.stamp.secs, _x.header.stamp.nsecs,) = _get_struct_3I().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.header.frame_id = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.header.frame_id = str[start:end]
      _x = self
      start = end
      end += 192
      (_x.des_v_x, _x.des_v_y, _x.des_v_z, _x.fb_a_x, _x.fb_a_y, _x.fb_a_z, _x.des_a_x, _x.des_a_y, _x.des_a_z, _x.des_q_x, _x.des_q_y, _x.des_q_z, _x.des_q_w, _x.des_thr, _x.hover_percentage, _x.thr_scale_compensate, _x.voltage, _x.err_axisang_x, _x.err_axisang_y, _x.err_axisang_z, _x.err_axisang_ang, _x.fb_rate_x, _x.fb_rate_y, _x.fb_rate_z,) = _get_struct_24d().unpack(str[start:end])
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill


  def serialize_numpy(self, buff, numpy):
    """
    serialize message with numpy array types into buffer
    :param buff: buffer, ``StringIO``
    :param numpy: numpy python module
    """
    try:
      _x = self
      buff.write(_get_struct_3I().pack(_x.header.seq, _x.header.stamp.secs, _x.header.stamp.nsecs))
      _x = self.header.frame_id
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self
      buff.write(_get_struct_24d().pack(_x.des_v_x, _x.des_v_y, _x.des_v_z, _x.fb_a_x, _x.fb_a_y, _x.fb_a_z, _x.des_a_x, _x.des_a_y, _x.des_a_z, _x.des_q_x, _x.des_q_y, _x.des_q_z, _x.des_q_w, _x.des_thr, _x.hover_percentage, _x.thr_scale_compensate, _x.voltage, _x.err_axisang_x, _x.err_axisang_y, _x.err_axisang_z, _x.err_axisang_ang, _x.fb_rate_x, _x.fb_rate_y, _x.fb_rate_z))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize_numpy(self, str, numpy):
    """
    unpack serialized message in str into this message instance using numpy for array types
    :param str: byte array of serialized message, ``str``
    :param numpy: numpy python module
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      if self.header is None:
        self.header = std_msgs.msg.Header()
      end = 0
      _x = self
      start = end
      end += 12
      (_x.header.seq, _x.header.stamp.secs, _x.header.stamp.nsecs,) = _get_struct_3I().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.header.frame_id = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.header.frame_id = str[start:end]
      _x = self
      start = end
      end += 192
      (_x.des_v_x, _x.des_v_y, _x.des_v_z, _x.fb_a_x, _x.fb_a_y, _x.fb_a_z, _x.des_a_x, _x.des_a_y, _x.des_a_z, _x.des_q_x, _x.des_q_y, _x.des_q_z, _x.des_q_w, _x.des_thr, _x.hover_percentage, _x.thr_scale_compensate, _x.voltage, _x.err_axisang_x, _x.err_axisang_y, _x.err_axisang_z, _x.err_axisang_ang, _x.fb_rate_x, _x.fb_rate_y, _x.fb_rate_z,) = _get_struct_24d().unpack(str[start:end])
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill

_struct_I = genpy.struct_I
def _get_struct_I():
    global _struct_I
    return _struct_I
_struct_24d = None
def _get_struct_24d():
    global _struct_24d
    if _struct_24d is None:
        _struct_24d = struct.Struct("<24d")
    return _struct_24d
_struct_3I = None
def _get_struct_3I():
    global _struct_3I
    if _struct_3I is None:
        _struct_3I = struct.Struct("<3I")
    return _struct_3I
