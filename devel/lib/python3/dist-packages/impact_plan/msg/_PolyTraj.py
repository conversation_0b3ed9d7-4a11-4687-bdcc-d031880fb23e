# This Python file uses the following encoding: utf-8
"""autogenerated by genpy from impact_plan/PolyTraj.msg. Do not edit."""
import codecs
import sys
python3 = True if sys.hexversion > 0x03000000 else False
import genpy
import struct

import genpy

class PolyTraj(genpy.Message):
  _md5sum = "7d47340fc475c0357eff322fb6ab494d"
  _type = "impact_plan/PolyTraj"
  _has_header = False  # flag to mark the presence of a Header object
  _full_text = """int16 drone_id
int32 traj_id
time start_time

uint8 order
float32[] coef_x
float32[] coef_y
float32[] coef_z
float32[] duration
"""
  __slots__ = ['drone_id','traj_id','start_time','order','coef_x','coef_y','coef_z','duration']
  _slot_types = ['int16','int32','time','uint8','float32[]','float32[]','float32[]','float32[]']

  def __init__(self, *args, **kwds):
    """
    Constructor. Any message fields that are implicitly/explicitly
    set to None will be assigned a default value. The recommend
    use is keyword arguments as this is more robust to future message
    changes.  You cannot mix in-order arguments and keyword arguments.

    The available fields are:
       drone_id,traj_id,start_time,order,coef_x,coef_y,coef_z,duration

    :param args: complete set of field values, in .msg order
    :param kwds: use keyword arguments corresponding to message field names
    to set specific fields.
    """
    if args or kwds:
      super(PolyTraj, self).__init__(*args, **kwds)
      # message fields cannot be None, assign default values for those that are
      if self.drone_id is None:
        self.drone_id = 0
      if self.traj_id is None:
        self.traj_id = 0
      if self.start_time is None:
        self.start_time = genpy.Time()
      if self.order is None:
        self.order = 0
      if self.coef_x is None:
        self.coef_x = []
      if self.coef_y is None:
        self.coef_y = []
      if self.coef_z is None:
        self.coef_z = []
      if self.duration is None:
        self.duration = []
    else:
      self.drone_id = 0
      self.traj_id = 0
      self.start_time = genpy.Time()
      self.order = 0
      self.coef_x = []
      self.coef_y = []
      self.coef_z = []
      self.duration = []

  def _get_types(self):
    """
    internal API method
    """
    return self._slot_types

  def serialize(self, buff):
    """
    serialize message into buffer
    :param buff: buffer, ``StringIO``
    """
    try:
      _x = self
      buff.write(_get_struct_hi2IB().pack(_x.drone_id, _x.traj_id, _x.start_time.secs, _x.start_time.nsecs, _x.order))
      length = len(self.coef_x)
      buff.write(_struct_I.pack(length))
      pattern = '<%sf'%length
      buff.write(struct.Struct(pattern).pack(*self.coef_x))
      length = len(self.coef_y)
      buff.write(_struct_I.pack(length))
      pattern = '<%sf'%length
      buff.write(struct.Struct(pattern).pack(*self.coef_y))
      length = len(self.coef_z)
      buff.write(_struct_I.pack(length))
      pattern = '<%sf'%length
      buff.write(struct.Struct(pattern).pack(*self.coef_z))
      length = len(self.duration)
      buff.write(_struct_I.pack(length))
      pattern = '<%sf'%length
      buff.write(struct.Struct(pattern).pack(*self.duration))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize(self, str):
    """
    unpack serialized message in str into this message instance
    :param str: byte array of serialized message, ``str``
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      if self.start_time is None:
        self.start_time = genpy.Time()
      end = 0
      _x = self
      start = end
      end += 15
      (_x.drone_id, _x.traj_id, _x.start_time.secs, _x.start_time.nsecs, _x.order,) = _get_struct_hi2IB().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      pattern = '<%sf'%length
      start = end
      s = struct.Struct(pattern)
      end += s.size
      self.coef_x = s.unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      pattern = '<%sf'%length
      start = end
      s = struct.Struct(pattern)
      end += s.size
      self.coef_y = s.unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      pattern = '<%sf'%length
      start = end
      s = struct.Struct(pattern)
      end += s.size
      self.coef_z = s.unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      pattern = '<%sf'%length
      start = end
      s = struct.Struct(pattern)
      end += s.size
      self.duration = s.unpack(str[start:end])
      self.start_time.canon()
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill


  def serialize_numpy(self, buff, numpy):
    """
    serialize message with numpy array types into buffer
    :param buff: buffer, ``StringIO``
    :param numpy: numpy python module
    """
    try:
      _x = self
      buff.write(_get_struct_hi2IB().pack(_x.drone_id, _x.traj_id, _x.start_time.secs, _x.start_time.nsecs, _x.order))
      length = len(self.coef_x)
      buff.write(_struct_I.pack(length))
      pattern = '<%sf'%length
      buff.write(self.coef_x.tostring())
      length = len(self.coef_y)
      buff.write(_struct_I.pack(length))
      pattern = '<%sf'%length
      buff.write(self.coef_y.tostring())
      length = len(self.coef_z)
      buff.write(_struct_I.pack(length))
      pattern = '<%sf'%length
      buff.write(self.coef_z.tostring())
      length = len(self.duration)
      buff.write(_struct_I.pack(length))
      pattern = '<%sf'%length
      buff.write(self.duration.tostring())
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize_numpy(self, str, numpy):
    """
    unpack serialized message in str into this message instance using numpy for array types
    :param str: byte array of serialized message, ``str``
    :param numpy: numpy python module
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      if self.start_time is None:
        self.start_time = genpy.Time()
      end = 0
      _x = self
      start = end
      end += 15
      (_x.drone_id, _x.traj_id, _x.start_time.secs, _x.start_time.nsecs, _x.order,) = _get_struct_hi2IB().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      pattern = '<%sf'%length
      start = end
      s = struct.Struct(pattern)
      end += s.size
      self.coef_x = numpy.frombuffer(str[start:end], dtype=numpy.float32, count=length)
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      pattern = '<%sf'%length
      start = end
      s = struct.Struct(pattern)
      end += s.size
      self.coef_y = numpy.frombuffer(str[start:end], dtype=numpy.float32, count=length)
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      pattern = '<%sf'%length
      start = end
      s = struct.Struct(pattern)
      end += s.size
      self.coef_z = numpy.frombuffer(str[start:end], dtype=numpy.float32, count=length)
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      pattern = '<%sf'%length
      start = end
      s = struct.Struct(pattern)
      end += s.size
      self.duration = numpy.frombuffer(str[start:end], dtype=numpy.float32, count=length)
      self.start_time.canon()
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill

_struct_I = genpy.struct_I
def _get_struct_I():
    global _struct_I
    return _struct_I
_struct_hi2IB = None
def _get_struct_hi2IB():
    global _struct_hi2IB
    if _struct_hi2IB is None:
        _struct_hi2IB = struct.Struct("<hi2IB")
    return _struct_hi2IB
