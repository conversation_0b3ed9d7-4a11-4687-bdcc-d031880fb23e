;; Auto-generated. Do not edit!


(when (boundp 'impact_plan::PolynomialTraj)
  (if (not (find-package "IMPACT_PLAN"))
    (make-package "IMPACT_PLAN"))
  (shadow 'PolynomialTraj (find-package "IMPACT_PLAN")))
(unless (find-package "IMPACT_PLAN::POLY<PERSON><PERSON>IALTRAJ")
  (make-package "IMPACT_PLAN::POLYNOMIALTRAJ"))

(in-package "ROS")
;;//! \htmlinclude PolynomialTraj.msg.html
(if (not (find-package "STD_MSGS"))
  (ros::roseus-add-msgs "std_msgs"))


(intern "*ACTION_ADD*" (find-package "IMPACT_PLAN::POLY<PERSON><PERSON><PERSON>LTRAJ"))
(shadow '*ACTION_ADD* (find-package "IMPACT_PLAN::POLYNOMIALTRAJ"))
(defconstant impact_plan::PolynomialTraj::*ACTION_ADD* 1)
(intern "*ACTION_ABORT*" (find-package "IMPACT_PLAN::P<PERSON>YN<PERSON><PERSON>LTRAJ"))
(shadow '*ACTION_ABORT* (find-package "IMPACT_PLAN::POLYNOMIALTRAJ"))
(defconstant impact_plan::PolynomialTraj::*ACTION_ABORT* 2)
(intern "*ACTION_WARN_START*" (find-package "IMPACT_PLAN::POLYNOMIALTRAJ"))
(shadow '*ACTION_WARN_START* (find-package "IMPACT_PLAN::POLYNOMIALTRAJ"))
(defconstant impact_plan::PolynomialTraj::*ACTION_WARN_START* 3)
(intern "*ACTION_WARN_FINAL*" (find-package "IMPACT_PLAN::POLYNOMIALTRAJ"))
(shadow '*ACTION_WARN_FINAL* (find-package "IMPACT_PLAN::POLYNOMIALTRAJ"))
(defconstant impact_plan::PolynomialTraj::*ACTION_WARN_FINAL* 4)
(intern "*ACTION_WARN_IMPOSSIBLE*" (find-package "IMPACT_PLAN::POLYNOMIALTRAJ"))
(shadow '*ACTION_WARN_IMPOSSIBLE* (find-package "IMPACT_PLAN::POLYNOMIALTRAJ"))
(defconstant impact_plan::PolynomialTraj::*ACTION_WARN_IMPOSSIBLE* 5)

(defun impact_plan::PolynomialTraj-to-symbol (const)
  (cond
        ((= const 1) 'impact_plan::PolynomialTraj::*ACTION_ADD*)
        ((= const 2) 'impact_plan::PolynomialTraj::*ACTION_ABORT*)
        ((= const 3) 'impact_plan::PolynomialTraj::*ACTION_WARN_START*)
        ((= const 4) 'impact_plan::PolynomialTraj::*ACTION_WARN_FINAL*)
        ((= const 5) 'impact_plan::PolynomialTraj::*ACTION_WARN_IMPOSSIBLE*)
        (t nil)))

(defclass impact_plan::PolynomialTraj
  :super ros::object
  :slots (_header _trajectory_id _action _trajectory ))

(defmethod impact_plan::PolynomialTraj
  (:init
   (&key
    ((:header __header) (instance std_msgs::Header :init))
    ((:trajectory_id __trajectory_id) 0)
    ((:action __action) 0)
    ((:trajectory __trajectory) ())
    )
   (send-super :init)
   (setq _header __header)
   (setq _trajectory_id (round __trajectory_id))
   (setq _action (round __action))
   (setq _trajectory __trajectory)
   self)
  (:header
   (&rest __header)
   (if (keywordp (car __header))
       (send* _header __header)
     (progn
       (if __header (setq _header (car __header)))
       _header)))
  (:trajectory_id
   (&optional __trajectory_id)
   (if __trajectory_id (setq _trajectory_id __trajectory_id)) _trajectory_id)
  (:action
   (&optional __action)
   (if __action (setq _action __action)) _action)
  (:trajectory
   (&rest __trajectory)
   (if (keywordp (car __trajectory))
       (send* _trajectory __trajectory)
     (progn
       (if __trajectory (setq _trajectory (car __trajectory)))
       _trajectory)))
  (:serialization-length
   ()
   (+
    ;; std_msgs/Header _header
    (send _header :serialization-length)
    ;; uint32 _trajectory_id
    4
    ;; uint32 _action
    4
    ;; impact_plan/PolynomialMatrix[] _trajectory
    (apply #'+ (send-all _trajectory :serialization-length)) 4
    ))
  (:serialize
   (&optional strm)
   (let ((s (if strm strm
              (make-string-output-stream (send self :serialization-length)))))
     ;; std_msgs/Header _header
       (send _header :serialize s)
     ;; uint32 _trajectory_id
       (write-long _trajectory_id s)
     ;; uint32 _action
       (write-long _action s)
     ;; impact_plan/PolynomialMatrix[] _trajectory
     (write-long (length _trajectory) s)
     (dolist (elem _trajectory)
       (send elem :serialize s)
       )
     ;;
     (if (null strm) (get-output-stream-string s))))
  (:deserialize
   (buf &optional (ptr- 0))
   ;; std_msgs/Header _header
     (send _header :deserialize buf ptr-) (incf ptr- (send _header :serialization-length))
   ;; uint32 _trajectory_id
     (setq _trajectory_id (sys::peek buf ptr- :integer)) (incf ptr- 4)
   ;; uint32 _action
     (setq _action (sys::peek buf ptr- :integer)) (incf ptr- 4)
   ;; impact_plan/PolynomialMatrix[] _trajectory
   (let (n)
     (setq n (sys::peek buf ptr- :integer)) (incf ptr- 4)
     (setq _trajectory (let (r) (dotimes (i n) (push (instance impact_plan::PolynomialMatrix :init) r)) r))
     (dolist (elem- _trajectory)
     (send elem- :deserialize buf ptr-) (incf ptr- (send elem- :serialization-length))
     ))
   ;;
   self)
  )

(setf (get impact_plan::PolynomialTraj :md5sum-) "953704bac8e4c900f7bb35217887d6c4")
(setf (get impact_plan::PolynomialTraj :datatype-) "impact_plan/PolynomialTraj")
(setf (get impact_plan::PolynomialTraj :definition-)
      "Header header

# the trajectory id, starts from \"1\".
uint32 trajectory_id

# the action command for trajectory server.
uint32 ACTION_ADD           =   1
uint32 ACTION_ABORT         =   2
uint32 ACTION_WARN_START           =   3
uint32 ACTION_WARN_FINAL           =   4
uint32 ACTION_WARN_IMPOSSIBLE      =   5
uint32 action

# the order of trajectory.
impact_plan/PolynomialMatrix[] trajectory 

================================================================================
MSG: std_msgs/Header
# Standard metadata for higher-level stamped data types.
# This is generally used to communicate timestamped data 
# in a particular coordinate frame.
# 
# sequence ID: consecutively increasing ID 
uint32 seq
#Two-integer timestamp that is expressed as:
# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')
# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')
# time-handling sugar is provided by the client library
time stamp
#Frame this data is associated with
string frame_id

================================================================================
MSG: impact_plan/PolynomialMatrix
# the order of trajectory.
uint32 num_order
uint32 num_dim

# the polynomial coecfficients of the trajectory.

float64[] data
float64 duration

")



(provide :impact_plan/PolynomialTraj "953704bac8e4c900f7bb35217887d6c4")


