#!/usr/bin/env python3
"""
Trajectory Statistics Analyzer for IMPACTOR System
Calculates detailed statistics from trajectory CSV files including:
- Average velocities and accelerations
- Total travel distances
- Flight duration and other metrics
"""

import sys
import pandas as pd
import numpy as np
from pathlib import Path
import argparse

def calculate_trajectory_statistics(csv_file):
    """
    Calculate comprehensive trajectory statistics from CSV file
    
    Args:
        csv_file: Path to the trajectory CSV file
        
    Returns:
        dict: Dictionary containing all calculated statistics
    """
    try:
        # Read CSV file
        df = pd.read_csv(csv_file)
        
        if len(df) < 2:
            print("Error: Not enough data points for analysis")
            return None
            
        # Calculate time differences
        time_diffs = np.diff(df['timestamp'])
        total_duration = df['timestamp'].iloc[-1] - df['timestamp'].iloc[0]
        
        # Calculate velocity magnitudes
        quad_vel_mag = np.sqrt(df['quad_vel_x']**2 + df['quad_vel_y']**2 + df['quad_vel_z']**2)
        load_vel_mag = np.sqrt(df['load_vel_x']**2 + df['load_vel_y']**2 + df['load_vel_z']**2)
        
        # Calculate acceleration magnitudes
        quad_acc_mag = np.sqrt(df['quad_acc_x']**2 + df['quad_acc_y']**2 + df['quad_acc_z']**2)
        load_acc_mag = np.sqrt(df['load_acc_x']**2 + df['load_acc_y']**2 + df['load_acc_z']**2)
        
        # Calculate travel distances using cumulative sum of distance between consecutive points
        quad_pos_diff = np.diff(df[['quad_pos_x', 'quad_pos_y', 'quad_pos_z']].values, axis=0)
        load_pos_diff = np.diff(df[['load_pos_x', 'load_pos_y', 'load_pos_z']].values, axis=0)
        
        quad_distances = np.sqrt(np.sum(quad_pos_diff**2, axis=1))
        load_distances = np.sqrt(np.sum(load_pos_diff**2, axis=1))
        
        quad_total_distance = np.sum(quad_distances)
        load_total_distance = np.sum(load_distances)
        
        # Calculate straight-line distances (start to end)
        quad_start = df[['quad_pos_x', 'quad_pos_y', 'quad_pos_z']].iloc[0].values
        quad_end = df[['quad_pos_x', 'quad_pos_y', 'quad_pos_z']].iloc[-1].values
        quad_straight_distance = np.sqrt(np.sum((quad_end - quad_start)**2))
        
        load_start = df[['load_pos_x', 'load_pos_y', 'load_pos_z']].iloc[0].values
        load_end = df[['load_pos_x', 'load_pos_y', 'load_pos_z']].iloc[-1].values
        load_straight_distance = np.sqrt(np.sum((load_end - load_start)**2))
        
        # Calculate average statistics
        stats = {
            # Basic info
            'total_duration': total_duration,
            'data_points': len(df),
            'sampling_rate': len(df) / total_duration if total_duration > 0 else 0,
            
            # Quadrotor statistics
            'quad_avg_velocity': np.mean(quad_vel_mag),
            'quad_max_velocity': np.max(quad_vel_mag),
            'quad_avg_acceleration': np.mean(quad_acc_mag),
            'quad_max_acceleration': np.max(quad_acc_mag),
            'quad_total_distance': quad_total_distance,
            'quad_straight_distance': quad_straight_distance,
            'quad_path_efficiency': quad_straight_distance / quad_total_distance if quad_total_distance > 0 else 0,
            
            # Load statistics
            'load_avg_velocity': np.mean(load_vel_mag),
            'load_max_velocity': np.max(load_vel_mag),
            'load_avg_acceleration': np.mean(load_acc_mag),
            'load_max_acceleration': np.max(load_acc_mag),
            'load_total_distance': load_total_distance,
            'load_straight_distance': load_straight_distance,
            'load_path_efficiency': load_straight_distance / load_total_distance if load_total_distance > 0 else 0,
            
            # Position info
            'quad_start_pos': quad_start,
            'quad_end_pos': quad_end,
            'load_start_pos': load_start,
            'load_end_pos': load_end,
        }
        
        return stats
        
    except Exception as e:
        print(f"Error analyzing trajectory: {e}")
        return None

def print_statistics(stats, csv_file):
    """
    Print formatted statistics to console
    
    Args:
        stats: Dictionary containing calculated statistics
        csv_file: Path to the CSV file for reference
    """
    if not stats:
        return
        
    print("\n" + "="*60)
    print("TRAJECTORY STATISTICS ANALYSIS")
    print("="*60)
    print(f"File: {Path(csv_file).name}")
    print(f"Duration: {stats['total_duration']:.2f} seconds")
    print(f"Data Points: {stats['data_points']}")
    print(f"Sampling Rate: {stats['sampling_rate']:.1f} Hz")
    
    print("\n" + "-"*30 + " QUADROTOR " + "-"*30)
    print(f"Start Position: ({stats['quad_start_pos'][0]:.3f}, {stats['quad_start_pos'][1]:.3f}, {stats['quad_start_pos'][2]:.3f}) m")
    print(f"End Position:   ({stats['quad_end_pos'][0]:.3f}, {stats['quad_end_pos'][1]:.3f}, {stats['quad_end_pos'][2]:.3f}) m")
    print(f"")
    print(f"Average Velocity:     {stats['quad_avg_velocity']:.3f} m/s")
    print(f"Maximum Velocity:     {stats['quad_max_velocity']:.3f} m/s")
    print(f"Average Acceleration: {stats['quad_avg_acceleration']:.3f} m/s²")
    print(f"Maximum Acceleration: {stats['quad_max_acceleration']:.3f} m/s²")
    print(f"")
    print(f"Total Travel Distance:    {stats['quad_total_distance']:.3f} m")
    print(f"Straight-line Distance:   {stats['quad_straight_distance']:.3f} m")
    print(f"Path Efficiency:          {stats['quad_path_efficiency']:.1%}")
    
    print("\n" + "-"*32 + " LOAD " + "-"*32)
    print(f"Start Position: ({stats['load_start_pos'][0]:.3f}, {stats['load_start_pos'][1]:.3f}, {stats['load_start_pos'][2]:.3f}) m")
    print(f"End Position:   ({stats['load_end_pos'][0]:.3f}, {stats['load_end_pos'][1]:.3f}, {stats['load_end_pos'][2]:.3f}) m")
    print(f"")
    print(f"Average Velocity:     {stats['load_avg_velocity']:.3f} m/s")
    print(f"Maximum Velocity:     {stats['load_max_velocity']:.3f} m/s")
    print(f"Average Acceleration: {stats['load_avg_acceleration']:.3f} m/s²")
    print(f"Maximum Acceleration: {stats['load_max_acceleration']:.3f} m/s²")
    print(f"")
    print(f"Total Travel Distance:    {stats['load_total_distance']:.3f} m")
    print(f"Straight-line Distance:   {stats['load_straight_distance']:.3f} m")
    print(f"Path Efficiency:          {stats['load_path_efficiency']:.1%}")
    
    print("\n" + "-"*28 + " SUMMARY " + "-"*28)
    print(f"System completed {stats['quad_straight_distance']:.1f}m mission in {stats['total_duration']:.1f}s")
    print(f"Average system velocity: {(stats['quad_avg_velocity'] + stats['load_avg_velocity'])/2:.2f} m/s")
    print(f"Quadrotor-Load distance: {np.sqrt(np.sum((stats['quad_end_pos'] - stats['load_end_pos'])**2)):.3f} m")
    print("="*60)

def main():
    parser = argparse.ArgumentParser(description='Analyze trajectory statistics from CSV files')
    parser.add_argument('csv_file', help='Path to trajectory CSV file')
    parser.add_argument('--quiet', '-q', action='store_true', help='Only print summary statistics')
    
    args = parser.parse_args()
    
    if not Path(args.csv_file).exists():
        print(f"Error: CSV file not found: {args.csv_file}")
        sys.exit(1)
    
    stats = calculate_trajectory_statistics(args.csv_file)
    if stats:
        if not args.quiet:
            print_statistics(stats, args.csv_file)
        else:
            # Print compact summary for script integration
            print(f"Duration: {stats['total_duration']:.2f}s, "
                  f"Quad: {stats['quad_avg_velocity']:.2f}m/s avg, {stats['quad_total_distance']:.1f}m total, "
                  f"Load: {stats['load_avg_velocity']:.2f}m/s avg, {stats['load_total_distance']:.1f}m total")
        sys.exit(0)
    else:
        sys.exit(1)

if __name__ == "__main__":
    main()
