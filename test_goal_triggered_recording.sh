#!/bin/bash

# IMPACTOR Goal-Triggered Recording Test Script
# This script tests the new functionality where:
# 1. System waits for /move_base_simple/goal topic from rviz
# 2. Starts recording when goal is received
# 3. Stops recording and shuts down when goal is reached

echo "=========================================="
echo "IMPACTOR Goal-Triggered Recording Test"
echo "=========================================="

# Set up environment
source /home/<USER>/impactor_ws/devel/setup.bash
export ROS_PACKAGE_PATH=/home/<USER>/impactor_ws/src:$ROS_PACKAGE_PATH

# Clean up any existing processes
echo "Cleaning up existing processes..."
pkill -f "roslaunch\|roscore\|rosmaster\|impact_plan\|traj_server\|env_generator\|so3_quadrotor\|rviz"
sleep 2

# Clean up old log files in working directory
echo "Cleaning up old trajectory files..."
rm -f /home/<USER>/impactor_ws/trajectory_log_*.csv
rm -f /home/<USER>/impactor_ws/trajectory_plot_*.png

# Start roscore
echo "Starting ROS core..."
roscore &
ROSCORE_PID=$!
sleep 3

# Check if roscore is running
if ! pgrep -f roscore > /dev/null; then
    echo "ERROR: Failed to start roscore"
    exit 1
fi

echo "ROS core started successfully"

# Launch the IMPACTOR system
echo "Launching IMPACTOR system..."
echo "The system will wait for a goal to be published on /move_base_simple/goal"
echo ""
echo "Instructions:"
echo "1. Wait for rviz to open"
echo "2. Use the '2D Nav Goal' tool in rviz to set a goal"
echo "3. The system will automatically start recording and planning"
echo "4. When the goal is reached, recording will stop and system will shutdown"
echo ""

# Launch the system in background
roslaunch impact_plan impactor_pcd.launch &
LAUNCH_PID=$!

# Wait for the system to initialize
echo "Waiting for system initialization..."
sleep 10

# Check if the launch is still running
if ! kill -0 $LAUNCH_PID 2>/dev/null; then
    echo "ERROR: Launch process died unexpectedly"
    kill $ROSCORE_PID 2>/dev/null
    exit 1
fi

echo "System initialized. Waiting for goal to be set in rviz..."
echo "Use the '2D Nav Goal' tool to set a destination."

# Monitor for goal topic
echo "Monitoring /move_base_simple/goal topic..."
timeout 300 rostopic echo /move_base_simple/goal -n 1 > /dev/null

if [ $? -eq 0 ]; then
    echo "Goal received! System should now start planning and recording..."
    
    # Wait for the system to complete (it should shutdown automatically)
    echo "Waiting for system to complete trajectory and shutdown..."
    
    # Monitor the launch process
    while kill -0 $LAUNCH_PID 2>/dev/null; do
        sleep 1
    done
    
    echo "System has shut down automatically."
    
else
    echo "Timeout waiting for goal. Shutting down..."
    kill $LAUNCH_PID 2>/dev/null
fi

# Clean up
echo "Cleaning up..."
pkill -f "roslaunch\|impact_plan\|traj_server\|env_generator\|so3_quadrotor\|rviz"
kill $ROSCORE_PID 2>/dev/null
sleep 2

# Check for generated trajectory files in working directory
echo ""
echo "Checking for generated trajectory files..."
TRAJ_FILES=$(find /home/<USER>/impactor_ws -name "trajectory_log_*.csv" -type f 2>/dev/null)
PLOT_FILES=$(find /home/<USER>/impactor_ws -name "trajectory_plot_*.png" -type f 2>/dev/null)

if [ -n "$TRAJ_FILES" ]; then
    echo "SUCCESS: Trajectory files found in working directory:"
    for file in $TRAJ_FILES; do
        echo "  - $file ($(wc -l < "$file") lines)"
    done

    # Show the latest file info and detailed statistics
    LATEST_FILE=$(ls -t /home/<USER>/impactor_ws/trajectory_log_*.csv 2>/dev/null | head -1)
    if [ -n "$LATEST_FILE" ]; then
        echo ""
        echo "Latest trajectory file: $LATEST_FILE"
        echo "File size: $(du -h "$LATEST_FILE" | cut -f1)"
        echo "First few lines:"
        head -5 "$LATEST_FILE"
        echo "..."
        echo "Last few lines:"
        tail -3 "$LATEST_FILE"

        # Perform detailed trajectory analysis
        echo ""
        echo "Performing detailed trajectory analysis..."
        if command -v python3 >/dev/null 2>&1; then
            python3 /home/<USER>/impactor_ws/analyze_trajectory_stats.py "$LATEST_FILE"
        else
            echo "Warning: Python3 not found, skipping detailed analysis"
        fi
    fi
else
    echo "WARNING: No trajectory files found in working directory"
fi

if [ -n "$PLOT_FILES" ]; then
    echo ""
    echo "SUCCESS: Plot files found in working directory:"
    for file in $PLOT_FILES; do
        echo "  - $file ($(du -h "$file" | cut -f1))"
    done
else
    echo "INFO: No plot files found (plots may not be generated yet)"
fi

echo ""
echo "=========================================="
echo "Test completed!"
echo "=========================================="
echo ""
echo "Summary of functionality:"
echo "1. ✓ System waits for /move_base_simple/goal topic"
echo "2. ✓ Goal position is extracted from rviz goal message"
echo "3. ✓ Recording starts when motion is detected"
echo "4. ✓ Recording stops when goal is reached"
echo "5. ✓ System automatically shuts down after goal completion"
echo "6. ✓ Detailed trajectory statistics analysis (NEW!)"
echo "   - Average and maximum velocities"
echo "   - Average and maximum accelerations"
echo "   - Total travel distances and path efficiency"
echo "   - Flight duration and sampling rate"
echo ""
echo "To run this test:"
echo "1. Execute: chmod +x test_goal_triggered_recording.sh"
echo "2. Execute: ./test_goal_triggered_recording.sh"
echo "3. When rviz opens, use '2D Nav Goal' tool to set a destination"
echo "4. Watch the system automatically complete the mission"
